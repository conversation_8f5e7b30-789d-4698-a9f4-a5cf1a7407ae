#!/usr/bin/env python3
"""
Test script to verify Gradio UI supports OpenAI-Compatible and HuggingFace-TEI providers
"""

import os
import sys

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_provider_lists():
    """Test that providers are properly loaded"""
    print("Testing provider configuration...")
    
    # Set test environment variables
    os.environ["OPENAI_API_KEY"] = "test-key"
    os.environ["OPENAI_COMPATIBLE_API_BASE"] = "http://test:8000/v1"
    os.environ["HUGGINGFACE_TEI_API_BASE"] = "http://test:8080"
    
    # Import after setting env vars
    from app import check_api_key, MODELS, EMBEDDING_MODELS
    
    # Check API keys
    api_keys_available = check_api_key()
    assert api_keys_available, "API keys should be available"
    
    # Check LLM providers
    print("\nLLM Providers:")
    for provider in MODELS.keys():
        print(f"  - {provider}")
    
    assert "OpenAI" in MODELS, "OpenAI should be available"
    assert "OpenAI-Compatible" in MODELS, "OpenAI-Compatible should be available"
    
    # Check embedding providers
    print("\nEmbedding Providers:")
    for provider in EMBEDDING_MODELS.keys():
        print(f"  - {provider}")
    
    assert "OpenAI" in EMBEDDING_MODELS, "OpenAI embeddings should be available"
    assert "HuggingFace-TEI" in EMBEDDING_MODELS, "HuggingFace-TEI should be available"
    
    print("\n✅ All provider tests passed!")


def test_model_provider_detection():
    """Test the get_model_provider function"""
    print("\nTesting model provider detection...")
    
    from app import get_model_provider
    
    # Test direct provider/model format
    assert get_model_provider("OpenAI/gpt-4o", None) == "OpenAI"
    assert get_model_provider(None, "HuggingFace-TEI/tei") == "HuggingFace-TEI"
    
    # Test custom model detection
    os.environ["OPENAI_COMPATIBLE_API_BASE"] = "http://test:8000/v1"
    os.environ["HUGGINGFACE_TEI_API_BASE"] = "http://test:8080"
    
    # Need to reload to pick up env changes
    import importlib
    import app
    importlib.reload(app)
    
    # Test custom model detection
    provider = app.get_model_provider("custom-llm-model", None)
    assert provider == "OpenAI-Compatible", f"Expected OpenAI-Compatible, got {provider}"
    
    provider = app.get_model_provider(None, "custom-embedding-model")
    assert provider == "HuggingFace-TEI", f"Expected HuggingFace-TEI, got {provider}"
    
    print("✅ Model provider detection tests passed!")


def test_gradio_components():
    """Test that Gradio components are properly configured"""
    print("\nTesting Gradio component configuration...")
    
    # Need to ensure env vars are set and module is reloaded
    os.environ["OPENAI_API_KEY"] = "test-key"
    os.environ["OPENAI_COMPATIBLE_API_BASE"] = "http://test:8000/v1"
    os.environ["HUGGINGFACE_TEI_API_BASE"] = "http://test:8080"
    
    # Reload to pick up env changes
    import importlib
    import app
    importlib.reload(app)
    
    # Call check_api_key to populate MODELS and EMBEDDING_MODELS
    app.check_api_key()
    
    # Re-import after reload
    from app import get_model_choices, get_embedding_model_choices, MODELS, EMBEDDING_MODELS
    
    print(f"  Available LLM providers: {list(MODELS.keys())}")
    print(f"  Available embedding providers: {list(EMBEDDING_MODELS.keys())}")
    
    # Test model choices
    openai_models = get_model_choices("OpenAI")
    print(f"  OpenAI models count: {len(openai_models)}")
    assert len(openai_models) > 0, "OpenAI should have models"
    
    openai_compatible_models = get_model_choices("OpenAI-Compatible")
    print(f"  OpenAI-Compatible models count: {len(openai_compatible_models)}")
    assert len(openai_compatible_models) > 0, "OpenAI-Compatible should have models"
    
    # Test embedding choices
    openai_embeddings = get_embedding_model_choices("OpenAI")
    print(f"  OpenAI embeddings count: {len(openai_embeddings)}")
    assert len(openai_embeddings) > 0, "OpenAI should have embeddings"
    
    tei_embeddings = get_embedding_model_choices("HuggingFace-TEI")
    print(f"  HuggingFace-TEI embeddings count: {len(tei_embeddings)}")
    assert len(tei_embeddings) > 0, "HuggingFace-TEI should have embeddings"
    
    print("✅ Gradio component tests passed!")


def main():
    print("Gradio Provider Support Test Suite")
    print("==================================\n")
    
    try:
        test_provider_lists()
        test_model_provider_detection()
        test_gradio_components()
        
        print("\n✅ All tests passed! Gradio UI properly supports OpenAI-Compatible and HuggingFace-TEI providers.")
        return 0
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())