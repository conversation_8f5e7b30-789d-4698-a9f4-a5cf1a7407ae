#!/usr/bin/env python3
"""
Test script for OpenAI-compatible API and Hugging Face TEI providers in CTINexus
"""

import os
import sys
import subprocess
import json

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_openai_compatible():
    """Test OpenAI-compatible API provider"""
    print("\n=== Testing OpenAI-Compatible API ===")
    
    # Check if environment variable is set
    api_base = os.getenv("OPENAI_COMPATIBLE_API_BASE")
    if not api_base:
        print("❌ OPENAI_COMPATIBLE_API_BASE not set in .env file")
        print("   Please set it to your OpenAI-compatible endpoint (e.g., http://localhost:8000/v1)")
        return False
    
    print(f"✓ API Base URL: {api_base}")
    
    # Test with a simple text
    test_text = "APT29 used PowerShell to download malware from *************"
    test_file = "test_input.txt"
    
    with open(test_file, 'w') as f:
        f.write(test_text)
    
    try:
        # Run CTINexus with OpenAI-compatible provider
        cmd = [
            "python", "app/app.py",
            "--input-file", test_file,
            "--provider", "OpenAI-Compatible",
            "--model", "custom-model",
            "--output", "test_openai_compatible_output.json"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ OpenAI-Compatible API test completed successfully")
            
            # Check if output file was created
            if os.path.exists("test_openai_compatible_output.json"):
                with open("test_openai_compatible_output.json", 'r') as f:
                    output = json.load(f)
                    print(f"✓ Extracted {len(output.get('IE', {}).get('triplets', []))} triplets")
                return True
            else:
                print("❌ Output file not created")
                return False
        else:
            print(f"❌ Test failed with error:\n{result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
        if os.path.exists("test_openai_compatible_output.json"):
            os.remove("test_openai_compatible_output.json")


def test_huggingface_tei():
    """Test Hugging Face TEI provider"""
    print("\n=== Testing Hugging Face TEI ===")
    
    # Check if environment variable is set
    api_base = os.getenv("HUGGINGFACE_TEI_API_BASE")
    if not api_base:
        print("❌ HUGGINGFACE_TEI_API_BASE not set in .env file")
        print("   Please set it to your TEI endpoint (e.g., http://localhost:8080)")
        return False
    
    print(f"✓ API Base URL: {api_base}")
    
    # Test with a simple text
    test_text = "APT29 used PowerShell to download malware from *************"
    test_file = "test_input.txt"
    
    with open(test_file, 'w') as f:
        f.write(test_text)
    
    try:
        # Run CTINexus with mixed providers
        cmd = [
            "python", "app/app.py",
            "--input-file", test_file,
            "--provider", "OpenAI",  # Use OpenAI for LLM
            "--model", "gpt-4o-mini",
            "--embedding-provider", "HuggingFace-TEI",  # Use TEI for embeddings
            "--embedding-model", "tei",
            "--output", "test_tei_output.json"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Hugging Face TEI test completed successfully")
            
            # Check if output file was created
            if os.path.exists("test_tei_output.json"):
                with open("test_tei_output.json", 'r') as f:
                    output = json.load(f)
                    print(f"✓ Extracted {len(output.get('IE', {}).get('triplets', []))} triplets")
                    print(f"✓ Entity alignment used TEI embeddings")
                return True
            else:
                print("❌ Output file not created")
                return False
        else:
            print(f"❌ Test failed with error:\n{result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
        if os.path.exists("test_tei_output.json"):
            os.remove("test_tei_output.json")


def test_mixed_providers():
    """Test using different providers for LLM and embeddings"""
    print("\n=== Testing Mixed Providers ===")
    
    # Check if both are available
    openai_key = os.getenv("OPENAI_API_KEY")
    tei_base = os.getenv("HUGGINGFACE_TEI_API_BASE")
    
    if not openai_key:
        print("⚠️  Skipping mixed provider test - OPENAI_API_KEY not set")
        return None
    
    if not tei_base:
        print("⚠️  Skipping mixed provider test - HUGGINGFACE_TEI_API_BASE not set")
        return None
    
    # Test with a simple text
    test_text = "APT29 used PowerShell to download malware from *************"
    test_file = "test_input.txt"
    
    with open(test_file, 'w') as f:
        f.write(test_text)
    
    try:
        # Run CTINexus with mixed providers
        cmd = [
            "python", "app/app.py",
            "--input-file", test_file,
            "--provider", "OpenAI",
            "--model", "gpt-4o-mini",
            "--embedding-provider", "HuggingFace-TEI",
            "--embedding-model", "tei",
            "--output", "test_mixed_output.json"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Mixed provider test completed successfully")
            print("✓ Used OpenAI for LLM tasks")
            print("✓ Used HuggingFace TEI for embeddings")
            return True
        else:
            print(f"❌ Test failed with error:\n{result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
        if os.path.exists("test_mixed_output.json"):
            os.remove("test_mixed_output.json")


def main():
    print("CTINexus Custom Provider Test Suite")
    print("===================================")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    results = []
    
    # Test OpenAI-compatible API
    if os.getenv("OPENAI_COMPATIBLE_API_BASE"):
        results.append(("OpenAI-Compatible API", test_openai_compatible()))
    else:
        print("\n⚠️  Skipping OpenAI-Compatible API test - OPENAI_COMPATIBLE_API_BASE not set")
    
    # Test Hugging Face TEI
    if os.getenv("HUGGINGFACE_TEI_API_BASE"):
        results.append(("Hugging Face TEI", test_huggingface_tei()))
    else:
        print("\n⚠️  Skipping Hugging Face TEI test - HUGGINGFACE_TEI_API_BASE not set")
    
    # Test mixed providers
    result = test_mixed_providers()
    if result is not None:
        results.append(("Mixed Providers", result))
    
    # Summary
    print("\n=== Test Summary ===")
    for name, success in results:
        status = "✓ PASSED" if success else "❌ FAILED"
        print(f"{name}: {status}")
    
    # Overall result
    all_passed = all(success for _, success in results)
    if all_passed and results:
        print("\n✅ All tests passed!")
        return 0
    elif not results:
        print("\n⚠️  No tests were run. Please configure at least one custom provider in .env")
        return 1
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())