#!/usr/bin/env python3
"""
Demo script showing the new provider support in CTINexus
"""

import os
import sys

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

print("CTINexus New Provider Support Demo")
print("==================================\n")

print("1. Setting up environment variables for demo...")
# Set demo environment variables
os.environ["OPENAI_API_KEY"] = "sk-demo-key"
os.environ["OPENAI_COMPATIBLE_API_BASE"] = "http://localhost:8000/v1"
os.environ["OPENAI_COMPATIBLE_API_KEY"] = "optional-key"
os.environ["HUGGINGFACE_TEI_API_BASE"] = "http://localhost:8080"
os.environ["HUGGINGFACE_TEI_API_KEY"] = "optional-key"

print("   ✓ OpenAI API configured")
print("   ✓ OpenAI-Compatible API endpoint: http://localhost:8000/v1")
print("   ✓ HuggingFace TEI endpoint: http://localhost:8080")

print("\n2. Loading CTINexus...")
from app import check_api_key, MODELS, EMBEDDING_MODELS, get_model_choices, get_embedding_model_choices

# Initialize providers
check_api_key()

print("\n3. Available Providers:")
print("\n   LLM Providers:")
for provider, models in MODELS.items():
    print(f"   - {provider}")
    for model_key, model_desc in list(models.items())[:2]:  # Show first 2 models
        print(f"     • {model_key}: {model_desc[:50]}...")

print("\n   Embedding Providers:")
for provider, models in EMBEDDING_MODELS.items():
    print(f"   - {provider}")
    for model_key, model_desc in list(models.items())[:2]:  # Show first 2 models
        print(f"     • {model_key}: {model_desc[:50]}...")

print("\n4. Example Configurations:")

print("\n   a) OpenAI-Compatible LLM + Default OpenAI Embeddings:")
print("      - LLM Provider: OpenAI-Compatible")
print("      - LLM Model: Select 'Other' → Enter 'llama-3-70b'")
print("      - Embedding Provider: OpenAI")
print("      - Embedding Model: text-embedding-3-large")

print("\n   b) OpenAI LLM + HuggingFace TEI Embeddings:")
print("      - LLM Provider: OpenAI")
print("      - LLM Model: gpt-4o-mini")
print("      - Embedding Provider: HuggingFace-TEI")
print("      - Embedding Model: tei (or 'Other' → 'BAAI/bge-base-en-v1.5')")

print("\n   c) Fully Custom Setup:")
print("      - LLM Provider: OpenAI-Compatible")
print("      - LLM Model: Select 'Other' → Enter 'mistral-7b-instruct'")
print("      - Embedding Provider: HuggingFace-TEI")
print("      - Embedding Model: Select 'Other' → Enter 'e5-large-v2'")

print("\n5. UI Interaction Flow:")
print("   1. Select LLM Provider from dropdown")
print("   2. Select Embedding Provider from dropdown (can be different!)")
print("   3. Choose models or select 'Other' for custom models")
print("   4. If 'Other' selected, enter custom model names")
print("   5. Paste threat intelligence text")
print("   6. Click 'Run' to process")

print("\n6. Command Line Usage Examples:")

print("\n   # OpenAI-Compatible API")
print("   python app/app.py -i report.txt --provider OpenAI-Compatible --model llama-3-70b")

print("\n   # Mixed providers")
print("   python app/app.py -i report.txt \\")
print("     --provider OpenAI --model gpt-4o \\")
print("     --embedding-provider HuggingFace-TEI --embedding-model tei")

print("\n   # Fully custom")
print("   python app/app.py -i report.txt \\")
print("     --provider OpenAI-Compatible --model mistral-7b \\")
print("     --embedding-provider HuggingFace-TEI --embedding-model bge-large")

print("\n✅ Demo complete! The Gradio UI now supports:")
print("   • Separate LLM and Embedding providers")
print("   • OpenAI-Compatible APIs (vLLM, LocalAI, etc.)")
print("   • HuggingFace Text Embeddings Inference")
print("   • Custom model names via 'Other' option")
print("   • Mix-and-match providers for flexibility")

print("\nTo start the UI: python app/app.py")
print("To test providers: python test_custom_providers.py")