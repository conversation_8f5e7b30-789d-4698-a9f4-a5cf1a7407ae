# OpenAI API Key
OPENAI_API_KEY=

# Gemini API Key
GEMINI_API_KEY=

# AWS Credentials
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=

# Ollama Configuration
# Set to the default (http://localhost:11434).
# For docker, set to http://host.docker.internal:11434
OLLAMA_BASE_URL=

# OpenAI-Compatible API Configuration
# Example: http://localhost:8000/v1 for vLLM, LocalAI, etc.
OPENAI_COMPATIBLE_API_BASE=
OPENAI_COMPATIBLE_API_KEY=

# Hugging Face Text Embeddings Inference Configuration
# Example: http://localhost:8080 for self-hosted TEI
HUGGINGFACE_TEI_API_BASE=
HUGGINGFACE_TEI_API_KEY=