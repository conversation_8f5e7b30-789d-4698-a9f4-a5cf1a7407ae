将给定三元组的主体和客体分类为以下类别之一：
[
    "账户",
    "凭证",
    "工具",
    "攻击者",
    "事件",
    "攻击目标",
    "指标": {
        "文件",
        "IP地址",
        "URL",
        "域名",
        "注册表项",
        "哈希值",
        "互斥体",
        "用户代理",
        "电子邮件",
        "Yara规则",
        "SSL证书",
    }
    "信息",
    "位置",
    "恶意软件",
    "恶意软件特征": {
        "行为",
        "能力",
        "特征",
        "载荷",
        "变种",
    }
    "组织",
    "基础设施",
    "时间",
    "漏洞",
    "此实体无法归类到任何现有类型"
]
您的响应必须是 JSON 格式，不能包含其他内容。
---------------------
示例 1：

"三元组": {"subject": "Akira勒索软件组织", "relation": "声称对...负责", "object": "最近的三次攻击"}

"标记的三元组": {"subject": {"text": "Akira勒索软件组织", "class": "攻击者"}, "relation": "声称对...负责", "object": {"text": "最近的三次攻击", "class": "事件"}} 
---------------------
示例 2：

"三元组": { "subject": "密钥", "relation": "嵌入在", "object": "ntuser.dat"}

"标记的三元组": {"subject": {"text": "密钥", "class": "凭证"}, "relation": "嵌入在", "object": {"text": "ntuser.dat", "class": "指标:文件"}}
---------------------
示例 3：

"三元组": {"subject": "APT35", "relation": "使用了", "object": "一种用于情报收集的新恶意软件变种"}

"标记的三元组": {"subject": {"text": "APT35", "class": "攻击者"}, "relation": "投放", "object": {"text": "一种用于情报收集的新恶意软件变种", "class": "恶意软件"}}
---------------------
示例 4：

"三元组": {"subject": "Cuba勒索软件行为者", "relation": "可能与...有关联", "object": "RomCom远程访问木马(RAT)行为者"}

"标记的三元组": {"subject": {"text": "Cuba勒索软件行为者", "class": "攻击者"},"relation": "可能与...有关联", "object": {"text": "RomCom远程访问木马(RAT)行为者", "class": "攻击者"}}
---------------------
示例 5：

"三元组": {"subject": "CVE-2023-36884", "relation": "允许攻击者构造", "object": "Microsoft Office文档"}

"标记的三元组": {"subject": {"text": "CVE-2023-36884", "class": "漏洞"}, "relation": "允许攻击者构造", "object": {"text": "Microsoft Office文档", "class": "指标:文件"}}
---------------------
示例 6：

"三元组": {"subject": "奇幻熊", "relation": "与...有关", "object": "俄罗斯政府"}

"标记的三元组": {"subject": {"text": "奇幻熊", "class": "攻击者"}, "relation": "与...有关", "object": {"text": "俄罗斯政府","class": "组织"}}
---------------------
示例 7：

"三元组": {"subject": "微软的SQL服务器", "relation": "为...保存数据", "object": "互联网服务和应用程序"}

"标记的三元组": {"subject": {"text": "微软的SQL服务器", "class": "基础设施"}, "relation": "为...保存数据", "object": {"text": "互联网服务和应用程序","class": "基础设施"}}
---------------------
示例 8：

"三元组": {"subject": "工具", "relation": "包括", "object": "PsExec.exe"}

"标记的三元组": {"subject": {"text": "工具", "class": "工具"}, "relation": "包括", "object": {"text": "PsExec.exe","class": "指标:文件"}}
---------------------
目标三元组：

{% for triple in triples %}
"三元组":: {{ triple }}

"标记的三元组": """请在此处输入您的答案"""
---------------------{% endfor %}

您的响应应遵循格式：{"tagged_triples": [`tagged_triple_1`, `tagged_triple_2`, ..., `tagged_triple_n`]}