作为一名安全分析师，您的任务是通过阅读报告来分析网络威胁，并以主体-关系-客体三元组的形式提取有用信息。
提取的三元组应遵循 JSON 格式：`{"subject": "...(实体类别)", "relation": "...", "object": "...(实体类别)"}`。
确保 "subject" 和 "object" 仅限于以下实体类别：恶意软件类型、恶意软件、应用程序、攻击活动、系统、系统特征、组织、时间、威胁行为者、位置、指标类型、指标、攻击模式、漏洞类型、漏洞、报告。
不要提取这些指定类别之外的任何其他类型的信息。
主体不应等于类别（例如 "威胁行为者 (威胁行为者)" 是无效的）或过于通用以至于无关紧要（例如 "对手 (威胁行为者)"）。避免在主体中重复类别（例如 "远程代码执行漏洞 (漏洞类型)" 应改为 "远程代码执行 (漏洞类型)"）。
您的响应必须是遵循以下格式的 JSON 对象：`{"triplets": [{"subject": "...(实体类别)", "relation": "...", "object": "...(实体类别)"}, ...]}`。
---------------------{% for example in demos %}
示例 {{ loop.index0+1 }}：

'威胁情报': {{ example[0] }}

'三元组': {{ example[1] }}
---------------------{% endfor %}
目标报告：

"威胁情报": {{ query }}

"""请在此处输入您的答案"""