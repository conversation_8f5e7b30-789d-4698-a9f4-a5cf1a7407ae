# Using CTINexus with Custom AI Providers

CTINexus supports custom AI providers through OpenAI-compatible APIs for LLMs and Hugging Face Text Embeddings Inference (TEI) for embeddings, allowing you to use self-hosted models or alternative providers.

## OpenAI-Compatible API (for LLMs)

### Overview

Any API that implements the OpenAI chat completions format can be used with CTINexus. This includes:
- vLLM servers
- LocalAI
- LM Studio
- FastChat
- Text Generation Inference (TGI)
- Any custom OpenAI-compatible endpoint

### Setup

#### 1. Configure Environment

In your `.env` file, set your API endpoint and optional API key:

```bash
# OpenAI-Compatible API Configuration
# Example: http://localhost:8000/v1 for vLLM, LocalAI, etc.
OPENAI_COMPATIBLE_API_BASE=http://localhost:8000/v1
OPENAI_COMPATIBLE_API_KEY=your-api-key-if-required
```

#### 2. Usage Examples

**Command Line:**
```bash
# Use with custom model name
python app.py -i report.txt --provider OpenAI-Compatible --model mistral-7b-instruct

# Specify different models for different stages
python app.py -i report.txt \
  --provider OpenAI-Compatible \
  --model llama-3-8b \
  --ie-model llama-3-8b \
  --et-model mistral-7b-instruct \
  --lp-model llama-3-8b
```

**Web Interface:**
1. Select "OpenAI-Compatible" as the provider
2. Enter your custom model name in the model selection field
3. Process your CTI text

### Example Providers

#### vLLM
```bash
# Start vLLM server
python -m vllm.entrypoints.openai.api_server \
  --model mistralai/Mistral-7B-Instruct-v0.2 \
  --port 8000

# Configure CTINexus
OPENAI_COMPATIBLE_API_BASE=http://localhost:8000/v1
```

#### LocalAI
```bash
# Start LocalAI
docker run -p 8080:8080 localai/localai:latest

# Configure CTINexus
OPENAI_COMPATIBLE_API_BASE=http://localhost:8080/v1
```

## Hugging Face Text Embeddings Inference (TEI)

### Overview

TEI provides high-performance inference for embedding models with an OpenAI-compatible API, supporting models like:
- BAAI/bge-base-en-v1.5
- sentence-transformers/all-MiniLM-L6-v2
- intfloat/e5-large-v2
- Any Hugging Face embedding model

### Setup

#### 1. Start TEI Server

**Using Docker:**
```bash
docker run -p 8080:80 \
  -v $PWD/data:/data \
  --name text-embeddings-inference \
  ghcr.io/huggingface/text-embeddings-inference:latest \
  --model-id BAAI/bge-base-en-v1.5
```

**Using Python:**
```bash
pip install text-embeddings-inference
text-embeddings-router --model-id BAAI/bge-base-en-v1.5 --port 8080
```

#### 2. Configure Environment

In your `.env` file:

```bash
# Hugging Face Text Embeddings Inference Configuration
# Example: http://localhost:8080 for self-hosted TEI
HUGGINGFACE_TEI_API_BASE=http://localhost:8080
HUGGINGFACE_TEI_API_KEY=your-api-key-if-required
```

#### 3. Usage Examples

**Command Line:**
```bash
# Use TEI for embeddings with OpenAI for LLMs
python app.py -i report.txt \
  --provider OpenAI \
  --model gpt-4o-mini \
  --embedding-provider HuggingFace-TEI \
  --embedding-model tei

# Use custom embedding model
python app.py -i report.txt \
  --provider OpenAI \
  --embedding-provider HuggingFace-TEI \
  --embedding-model bge-base-en-v1.5
```

**Web Interface:**
1. Select your LLM provider (e.g., OpenAI)
2. Select "HuggingFace-TEI" as the embedding provider
3. Choose "tei" for default model or enter custom model name
4. Process your CTI text

## Combining Custom Providers

You can mix and match providers for maximum flexibility:

```bash
# Use OpenAI-compatible LLM with TEI embeddings
python app.py -i report.txt \
  --provider OpenAI-Compatible \
  --model llama-3-70b \
  --embedding-provider HuggingFace-TEI \
  --embedding-model e5-large-v2

# Use Ollama for LLMs with TEI for embeddings
python app.py -i report.txt \
  --provider Ollama \
  --model llama3.1:70b \
  --embedding-provider HuggingFace-TEI \
  --embedding-model bge-large-en-v1.5
```

## Performance Considerations

### TEI Performance
TEI can achieve significant cost savings compared to commercial APIs:
- Up to 64x cheaper than OpenAI embeddings
- Throughput: 450+ req/sec on NVIDIA A10G
- Supports dynamic batching for optimal resource utilization

### OpenAI-Compatible API Performance
Performance depends on your backend implementation:
- vLLM: High-throughput with continuous batching
- LocalAI: Good for CPU inference
- TGI: Optimized for Hugging Face models

## Troubleshooting

### Common Issues

1. **Connection refused error:**
   - Ensure your API server is running
   - Check the API base URL and port
   - For Docker users, use `host.docker.internal` instead of `localhost`

2. **Model not found:**
   - Verify the model name matches exactly what your server expects
   - Some servers require specific model naming formats

3. **Authentication errors:**
   - Check if your server requires API keys
   - Ensure the API key environment variable is set correctly

4. **JSON parsing errors:**
   - Some models may not support JSON output format
   - Try different models or adjust server settings

### Debug Mode

Enable verbose logging to troubleshoot issues:
```bash
export LITELLM_LOG=DEBUG
python app.py -i report.txt --provider OpenAI-Compatible --model your-model
```

## Testing Your Setup

A test script is provided to verify your custom provider configuration:

```bash
# Test all configured custom providers
python test_custom_providers.py

# The script will:
# - Check if environment variables are set
# - Run a simple CTI extraction test
# - Verify the output
# - Show a summary of results
```

## Advanced Configuration

### Custom Headers and Parameters

For APIs requiring special headers or parameters, you can modify the request format by setting additional environment variables or updating the provider configuration in the code.

### Load Balancing

For production deployments, consider using multiple API endpoints with a load balancer for improved reliability and performance.