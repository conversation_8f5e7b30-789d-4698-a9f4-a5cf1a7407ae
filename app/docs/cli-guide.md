# CTINexus Command Line Interface

CTINexus provides a powerful command line interface (CLI) for processing threat intelligence reports without the need for a graphical interface. This is ideal for automation, batch processing, and integration into existing security workflows.

## Quick Start

### Basic Usage

```bash
# Process text directly
python app.py --text "Your threat intelligence text here"

# Process a file
python app.py --input-file report.txt

# Use specific models
python app.py --input-file report.txt --model gpt-4o --embedding-model text-embedding-3-large
```

## Command Line Options

### Input Options

| Option | Short | Description |
|--------|-------|-------------|
| `--text TEXT` | `-t` | Input threat intelligence text to process directly |
| `--input-file FILE` | `-i` | Path to file containing threat intelligence text |

**Note**: `--text` and `--input-file` are mutually exclusive - use one or the other.

### Model Selection

| Option | Description | Example |
|--------|-------------|---------|
| `--provider` | AI provider to use (auto-detected if not specified) | `--provider OpenAI` |
| `--model` | Model to use for all text processing steps | `--model gpt-4o` |
| `--embedding-provider` | Embedding provider to use (defaults to --provider) | `--embedding-provider HuggingFace-TEI` |
| `--embedding-model` | Embedding model for entity alignment | `--embedding-model text-embedding-3-large` |

#### Provider-Specific Models

**OpenAI Models:**
- `o4-mini`, `o3-mini`, `o3`, `o3-pro` (Reasoning models)
- `gpt-4.1`, `gpt-4o`, `gpt-4`, `gpt-4-turbo` (GPT models)
- `gpt-4.1-mini`, `gpt-4o-mini`, `gpt-4.1-nano` (Smaller models)

**Gemini Models:**
- `gemini-2.5-flash-lite`, `gemini-2.0-flash`, `gemini-2.0-flash-lite`

**AWS Models:**
- `anthropic.claude-3-7-sonnet`, `anthropic.claude-3-5-sonnet`
- `anthropic.claude-3-5-haiku`, `anthropic.claude-3-haiku`
- `amazon.nova-micro-v1:0`, `amazon.nova-lite-v1:0`, `amazon.nova-pro-v1:0`
- `deepseek.r1-v1:0`, `mistral.pixtral-large-2502-v1:0`
- Meta Llama models: `meta.llama3-1-8b-instruct-v1:0`, etc.

**Ollama Models:**
- `llama3.1:8b`, `llama3.1:70b`, `llama3:8b` (Llama models)
- `mistral:7b`, `mixtral:8x7b` (Mistral models)
- `qwen2.5:7b`, `qwen2.5:14b` (Multilingual models)
- `phi3:14b`, `gemma2:9b`, `gemma2:27b` (Other open models)

**OpenAI-Compatible API:**
- Any model name supported by your OpenAI-compatible endpoint
- Examples: `mistral-7b-instruct`, `llama-3-70b`, `custom-model`

**HuggingFace-TEI (Embedding Models):**
- `tei` (Default model configured on the server)
- Any Hugging Face embedding model ID
- Examples: `BAAI/bge-base-en-v1.5`, `sentence-transformers/all-MiniLM-L6-v2`

### Fine-Grained Model Control

The default models can be overridden with specific models for specific tasks in the pipeline:

| Option | Description |
|--------|-------------|
| `--ie-model` | Override model for Intelligence Extraction |
| `--et-model` | Override model for Entity Tagging |
| `--ea-model` | Override embedding model for Entity Alignment |
| `--lp-model` | Override model for Link Prediction |

### Pipeline Configuration

| Option | Default | Description |
|--------|---------|-------------|
| `--similarity-threshold` | 0.6 | Similarity threshold for entity alignment (0.0-1.0) |

### Output Options

| Option | Short | Description |
|--------|-------|-------------|
| `--output FILE` | `-o` | Output file path (default: organized in app/output/ directory) |

**Default output behavior:**
- For a file input: `app/output/<input_file>_output.json`
- For a text input: `app/output/output.json`
- Custom output: Uses the exact path provided by user

## Usage Examples

### Basic Processing

```bash
# Process a threat report file with default models
python app.py --input-file threat_report.txt

# Process text directly
python app.py --text "APT29 used PowerShell to download additional malware from *************"
```

### Provider and Model Selection

```bash
# Use OpenAI with specific models
python app.py -i report.txt --provider OpenAI --model gpt-4o --embedding-model text-embedding-3-large

# Use Gemini with default models
python app.py -i report.txt --provider Gemini

# Use AWS Claude
python app.py -i report.txt --provider AWS --model anthropic.claude-3-5-sonnet

# Use Ollama (local models)
python app.py -i report.txt --provider Ollama --model qwen2.5:7b --embedding-model nomic-embed-text

# Use Ollama with default models
python app.py -i report.txt --provider Ollama

# Use OpenAI-Compatible API
python app.py -i report.txt --provider OpenAI-Compatible --model llama-3-70b

# Mix providers: OpenAI for LLM, HuggingFace TEI for embeddings
python app.py -i report.txt \
  --provider OpenAI --model gpt-4o-mini \
  --embedding-provider HuggingFace-TEI --embedding-model tei
```

### Advanced Model Configuration

```bash
# Use different models for each pipeline step
python app.py -i report.txt \
  --ie-model gpt-4o \
  --et-model gpt-4o-mini \
  --ea-model text-embedding-3-large \
  --lp-model gpt-4o-mini

# Adjust similarity threshold for stricter entity alignment
python app.py -i report.txt --model gpt-4o --similarity-threshold 0.8
```

### Output Control

```bash
# Use organized default output (saves to app/output/)
python app.py -i report.txt --model gpt-4o
# Creates: app/output/report_output.json

# Specify custom output file
python app.py -i report.txt --output analysis_results.json
# Creates: analysis_results.json (in current directory)
```

## API Configuration

Before using the CLI, ensure your API keys are configured in the `.env` file:

```bash
# Copy the example file
cp .env.example .env

# Edit with your API keys
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# For Ollama, set to the default (http://localhost:11434) or your custom URL.
OLLAMA_BASE_URL=http://localhost:11434

# OpenAI-Compatible API Configuration (optional)
OPENAI_COMPATIBLE_API_BASE=http://localhost:8000/v1
OPENAI_COMPATIBLE_API_KEY=your_api_key_if_required

# Hugging Face Text Embeddings Inference Configuration (optional)
HUGGINGFACE_TEI_API_BASE=http://localhost:8080
HUGGINGFACE_TEI_API_KEY=your_api_key_if_required
```

You only need to configure one provider, but multiple providers can be set up for flexibility. See the [Custom Providers Guide](custom-providers-guide.md) for detailed setup instructions for OpenAI-compatible APIs and Hugging Face TEI.

### Getting Help

```bash
# Show all available options
python app.py --help
```
