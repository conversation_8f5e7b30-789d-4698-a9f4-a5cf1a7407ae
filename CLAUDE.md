# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application

**Local Setup:**
```bash
# Install dependencies
pip install -r requirements.txt

# Run web interface (Gradio UI)
python app/app.py
# Access at: http://127.0.0.1:7860

# Run from app directory
cd app && python app.py
```

**Docker Setup:**
```bash
# Build and start
docker compose up --build

# Run in background
docker compose up -d --build

# Stop application
docker compose down
# Access at: http://localhost:8000
```

### Command Line Interface
```bash
# Process text directly
python app/app.py --text "threat intelligence text"

# Process file
python app/app.py --input-file report.txt

# With specific models
python app/app.py --input-file report.txt --model gpt-4o --embedding-model text-embedding-3-large

# Show help
python app/app.py --help
```

### Environment Configuration
```bash
# Copy template and configure API keys
cp .env.example .env
# Edit .env with your API credentials (OpenAI, Gemini, AWS, or Ollama)
```

## Architecture Overview

CTINexus is a framework for automatic cyber threat intelligence (CTI) knowledge extraction and cybersecurity knowledge graph (CSKG) construction using LLMs with optimized in-context learning.

### Core Pipeline Components

1. **Intelligence Extraction (IE)** - `llm_processor.py:LLMExtractor`
   - Extracts cybersecurity entities and relationships from unstructured text
   - Uses demonstration retrieval for few-shot learning
   - Template: `prompts/ie.jinja`

2. **Entity Typing (ET)** - `llm_processor.py:LLMTagger`
   - Groups mentions by semantic type
   - Template: `prompts/et.jinja`

3. **Entity Alignment/Merging** - `graph_constructor.py:Merger`
   - Canonicalizes extracted knowledge and removes redundancy
   - Uses embedding-based similarity matching with configurable threshold
   - IOC (Indicator of Compromise) protection during merging

4. **Link Prediction (LP)** - `graph_constructor.py:Linker`
   - Predicts missing relationships to complete the knowledge graph
   - Template: `prompts/link.jinja`

5. **Graph Visualization** - `graph_constructor.py:create_graph_visualization`
   - Interactive network visualization using pyvis/plotly

### Key Files Structure

- **`app/app.py`** - Main application entry point with Gradio UI and CLI interface
- **`app/cti_processor.py`** - Text preprocessing and post-processing utilities
- **`app/llm_processor.py`** - LLM-based extraction and tagging components
- **`app/graph_constructor.py`** - Entity alignment, linking, and graph visualization
- **`app/config/config.yaml`** - Configuration for models, prompts, and pipeline parameters
- **`app/data/`** - Contains demonstration examples and test data
  - `demo/` - Few-shot learning examples for demonstration retrieval
  - `test/` - Test dataset for evaluation
- **`app/prompts/`** - Jinja2 templates for different pipeline stages

### Configuration Management

Uses Hydra for configuration management. Key settings in `config/config.yaml`:
- `provider`, `model`, `embedding_model` - AI provider and model selection
- `similarity_threshold` - Entity alignment threshold (0.0-1.0)
- `retriever` - Demonstration retrieval configuration (kNN vs random)
- `shot` - Number of examples for few-shot learning

### Multi-Provider LLM Support

Supports multiple AI providers with automatic API key detection:
- **OpenAI**: GPT models and embedding models
- **Gemini**: Google's language models  
- **AWS**: Claude and other Bedrock models
- **Ollama**: Local open-source models

Model selection can be:
- Global (affects all pipeline stages)
- Per-stage specific (`--ie-model`, `--et-model`, `--ea-model`, `--lp-model`)

### Data Processing

- Input: Unstructured threat intelligence text (reports, articles, etc.)
- Output: JSON with extracted entities, relationships, and interactive graph visualization
- The `app/data/` directory contains structured threat intelligence examples used for demonstration retrieval

### Testing

No formal test framework found. The `app/data/test/` directory contains threat intelligence samples that can be used for manual testing.