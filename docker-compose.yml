services:

  ctinexus:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
      - "57623-57628:57623-57628"
    environment:
      - GRADIO_SERVER_NAME=0.0.0.0
      - GRADIO_SERVER_PORT=8000
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL}
    env_file:
      - .env
    networks:
      - ctinexus_network
    volumes:
      - ./app:/app

networks:
  ctinexus_network:
    driver: bridge

volumes:
  falkordb_data: